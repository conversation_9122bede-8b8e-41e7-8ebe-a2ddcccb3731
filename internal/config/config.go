package config

import (
	"fmt"
	"os"
	"path/filepath"

	"gopkg.in/yaml.v3"
	"license-server-wrapper/pkg/types"
)

const (
	// DefaultConfigFile 默认配置文件名
	DefaultConfigFile = "vmmanager.yaml"
	// ConfigDirName 配置目录名
	ConfigDirName = ".vmmanager"
)

// Manager 配置管理器
type Manager struct {
	configPath string
	config     *types.Config
}

// NewManager 创建新的配置管理器
func NewManager() *Manager {
	return &Manager{
		configPath: getDefaultConfigPath(),
		config:     types.DefaultConfig(),
	}
}

// NewManagerWithPath 使用指定路径创建配置管理器
func NewManagerWithPath(configPath string) *Manager {
	return &Manager{
		configPath: configPath,
		config:     types.DefaultConfig(),
	}
}

// Load 加载配置文件
func (m *Manager) Load() error {
	// 检查配置文件是否存在
	if _, err := os.Stat(m.configPath); os.IsNotExist(err) {
		// 配置文件不存在，创建默认配置
		return m.Save()
	}

	// 读取配置文件
	data, err := os.ReadFile(m.configPath)
	if err != nil {
		return fmt.Errorf("读取配置文件失败: %w", err)
	}

	// 解析YAML
	if err := yaml.Unmarshal(data, m.config); err != nil {
		return fmt.Errorf("解析配置文件失败: %w", err)
	}

	// 验证配置
	if err := m.validateConfig(); err != nil {
		return fmt.Errorf("配置验证失败: %w", err)
	}

	return nil
}

// Save 保存配置文件
func (m *Manager) Save() error {
	// 确保配置目录存在
	configDir := filepath.Dir(m.configPath)
	if err := os.MkdirAll(configDir, 0755); err != nil {
		return fmt.Errorf("创建配置目录失败: %w", err)
	}

	// 序列化配置
	data, err := yaml.Marshal(m.config)
	if err != nil {
		return fmt.Errorf("序列化配置失败: %w", err)
	}

	// 写入文件
	if err := os.WriteFile(m.configPath, data, 0644); err != nil {
		return fmt.Errorf("写入配置文件失败: %w", err)
	}

	return nil
}

// GetConfig 获取配置
func (m *Manager) GetConfig() *types.Config {
	return m.config
}

// SetConfig 设置配置
func (m *Manager) SetConfig(config *types.Config) {
	m.config = config
}

// UpdateVMConfig 更新虚拟机配置
func (m *Manager) UpdateVMConfig(vmInfo types.VMInfo) error {
	m.config.VM = vmInfo
	return m.Save()
}

// UpdateSecurityConfig 更新安全配置
func (m *Manager) UpdateSecurityConfig(secConfig types.SecurityConfig) error {
	m.config.Security = secConfig
	return m.Save()
}

// UpdateTrayConfig 更新托盘配置
func (m *Manager) UpdateTrayConfig(trayConfig types.TrayConfig) error {
	m.config.Tray = trayConfig
	return m.Save()
}

// UpdateLogConfig 更新日志配置
func (m *Manager) UpdateLogConfig(logConfig types.LogConfig) error {
	m.config.Log = logConfig
	return m.Save()
}

// GetConfigPath 获取配置文件路径
func (m *Manager) GetConfigPath() string {
	return m.configPath
}

// validateConfig 验证配置的有效性
func (m *Manager) validateConfig() error {
	// 验证虚拟机配置
	if m.config.VM.VMXPath == "" {
		return fmt.Errorf("虚拟机VMX文件路径不能为空")
	}

	// 检查VMX文件是否存在
	if _, err := os.Stat(m.config.VM.VMXPath); os.IsNotExist(err) {
		return fmt.Errorf("VMX文件不存在: %s", m.config.VM.VMXPath)
	}

	// 验证vmrun路径
	if m.config.VMRunPath == "" {
		m.config.VMRunPath = "vmrun.exe" // 使用默认值
	}

	// 验证安全配置
	if m.config.Security.ValidationTimeout <= 0 {
		m.config.Security.ValidationTimeout = 30
	}

	if m.config.Security.MaxRetries <= 0 {
		m.config.Security.MaxRetries = 3
	}

	// 验证托盘配置
	if m.config.Tray.UpdateInterval <= 0 {
		m.config.Tray.UpdateInterval = 5
	}

	// 验证日志配置
	if m.config.Log.Level == "" {
		m.config.Log.Level = "info"
	}

	if m.config.Log.MaxSize <= 0 {
		m.config.Log.MaxSize = 10
	}

	if m.config.Log.MaxBackups <= 0 {
		m.config.Log.MaxBackups = 5
	}

	if m.config.Log.MaxAge <= 0 {
		m.config.Log.MaxAge = 30
	}

	return nil
}

// getDefaultConfigPath 获取默认配置文件路径
func getDefaultConfigPath() string {
	// 获取用户主目录
	homeDir, err := os.UserHomeDir()
	if err != nil {
		// 如果获取失败，使用当前目录
		return DefaultConfigFile
	}

	// 返回用户主目录下的配置文件路径
	return filepath.Join(homeDir, ConfigDirName, DefaultConfigFile)
}

// GetDefaultConfigDir 获取默认配置目录
func GetDefaultConfigDir() string {
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return "."
	}
	return filepath.Join(homeDir, ConfigDirName)
}

// Exists 检查配置文件是否存在
func (m *Manager) Exists() bool {
	_, err := os.Stat(m.configPath)
	return !os.IsNotExist(err)
}

// Reset 重置为默认配置
func (m *Manager) Reset() error {
	m.config = types.DefaultConfig()
	return m.Save()
}

// Backup 备份当前配置
func (m *Manager) Backup() error {
	if !m.Exists() {
		return fmt.Errorf("配置文件不存在，无法备份")
	}

	backupPath := m.configPath + ".backup"
	
	// 读取当前配置
	data, err := os.ReadFile(m.configPath)
	if err != nil {
		return fmt.Errorf("读取配置文件失败: %w", err)
	}

	// 写入备份文件
	if err := os.WriteFile(backupPath, data, 0644); err != nil {
		return fmt.Errorf("创建备份文件失败: %w", err)
	}

	return nil
}

// Restore 从备份恢复配置
func (m *Manager) Restore() error {
	backupPath := m.configPath + ".backup"
	
	// 检查备份文件是否存在
	if _, err := os.Stat(backupPath); os.IsNotExist(err) {
		return fmt.Errorf("备份文件不存在")
	}

	// 读取备份文件
	data, err := os.ReadFile(backupPath)
	if err != nil {
		return fmt.Errorf("读取备份文件失败: %w", err)
	}

	// 写入当前配置文件
	if err := os.WriteFile(m.configPath, data, 0644); err != nil {
		return fmt.Errorf("恢复配置文件失败: %w", err)
	}

	// 重新加载配置
	return m.Load()
}
