package logger

import (
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"

	"github.com/sirupsen/logrus"
	"license-server-wrapper/pkg/types"
)

// Logger 日志管理器
type Logger struct {
	*logrus.Logger
	config *types.LogConfig
	file   *os.File
}

// NewLogger 创建新的日志管理器
func NewLogger(config *types.LogConfig) (*Logger, error) {
	logger := &Logger{
		Logger: logrus.New(),
		config: config,
	}

	if err := logger.setup(); err != nil {
		return nil, fmt.Errorf("设置日志失败: %w", err)
	}

	return logger, nil
}

// setup 设置日志配置
func (l *Logger) setup() error {
	// 设置日志级别
	level, err := logrus.ParseLevel(l.config.Level)
	if err != nil {
		level = logrus.InfoLevel
		l.Logger.WithError(err).Warn("解析日志级别失败，使用默认级别 info")
	}
	l.Logger.SetLevel(level)

	// 设置日志格式
	l.Logger.SetFormatter(&logrus.TextFormatter{
		FullTimestamp:   true,
		TimestampFormat: "2006-01-02 15:04:05",
		ForceColors:     false,
	})

	// 设置输出
	if err := l.setupOutput(); err != nil {
		return err
	}

	return nil
}

// setupOutput 设置日志输出
func (l *Logger) setupOutput() error {
	var writers []io.Writer

	// 总是输出到控制台
	writers = append(writers, os.Stdout)

	// 如果配置了文件输出
	if l.config.File != "" {
		// 确保日志目录存在
		logDir := filepath.Dir(l.config.File)
		if err := os.MkdirAll(logDir, 0755); err != nil {
			return fmt.Errorf("创建日志目录失败: %w", err)
		}

		// 打开日志文件
		file, err := os.OpenFile(l.config.File, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
		if err != nil {
			return fmt.Errorf("打开日志文件失败: %w", err)
		}

		l.file = file
		writers = append(writers, file)
	}

	// 设置多重输出
	l.Logger.SetOutput(io.MultiWriter(writers...))

	return nil
}

// Close 关闭日志文件
func (l *Logger) Close() error {
	if l.file != nil {
		return l.file.Close()
	}
	return nil
}

// UpdateConfig 更新日志配置
func (l *Logger) UpdateConfig(config *types.LogConfig) error {
	// 关闭旧的文件句柄
	if l.file != nil {
		l.file.Close()
		l.file = nil
	}

	l.config = config
	return l.setup()
}

// WithVM 创建带有虚拟机信息的日志条目
func (l *Logger) WithVM(vmName string) *logrus.Entry {
	return l.Logger.WithField("vm", vmName)
}

// WithOperation 创建带有操作信息的日志条目
func (l *Logger) WithOperation(operation string) *logrus.Entry {
	return l.Logger.WithField("operation", operation)
}

// WithError 创建带有错误信息的日志条目
func (l *Logger) WithError(err error) *logrus.Entry {
	return l.Logger.WithError(err)
}

// WithFields 创建带有多个字段的日志条目
func (l *Logger) WithFields(fields map[string]interface{}) *logrus.Entry {
	return l.Logger.WithFields(logrus.Fields(fields))
}

// LogVMOperation 记录虚拟机操作
func (l *Logger) LogVMOperation(vmName string, operation string, success bool, err error, duration string) {
	entry := l.Logger.WithFields(logrus.Fields{
		"vm":        vmName,
		"operation": operation,
		"success":   success,
		"duration":  duration,
	})

	if success {
		entry.Info("虚拟机操作成功")
	} else {
		if err != nil {
			entry.WithError(err).Error("虚拟机操作失败")
		} else {
			entry.Error("虚拟机操作失败")
		}
	}
}

// LogSecurityEvent 记录安全事件
func (l *Logger) LogSecurityEvent(event string, success bool, details map[string]interface{}) {
	entry := l.Logger.WithFields(logrus.Fields{
		"event":   event,
		"success": success,
		"type":    "security",
	})

	if details != nil {
		entry = entry.WithFields(logrus.Fields(details))
	}

	if success {
		entry.Info("安全事件")
	} else {
		entry.Warn("安全事件失败")
	}
}

// LogSystemEvent 记录系统事件
func (l *Logger) LogSystemEvent(event string, level string, details map[string]interface{}) {
	entry := l.Logger.WithFields(logrus.Fields{
		"event": event,
		"type":  "system",
	})

	if details != nil {
		entry = entry.WithFields(logrus.Fields(details))
	}

	switch strings.ToLower(level) {
	case "debug":
		entry.Debug("系统事件")
	case "info":
		entry.Info("系统事件")
	case "warn", "warning":
		entry.Warn("系统事件")
	case "error":
		entry.Error("系统事件")
	case "fatal":
		entry.Fatal("系统事件")
	default:
		entry.Info("系统事件")
	}
}

// GetLogLevel 获取当前日志级别
func (l *Logger) GetLogLevel() string {
	return l.Logger.GetLevel().String()
}

// SetLogLevel 设置日志级别
func (l *Logger) SetLogLevel(level string) error {
	logLevel, err := logrus.ParseLevel(level)
	if err != nil {
		return fmt.Errorf("无效的日志级别: %s", level)
	}

	l.Logger.SetLevel(logLevel)
	l.config.Level = level
	return nil
}

// GetLogFile 获取日志文件路径
func (l *Logger) GetLogFile() string {
	return l.config.File
}

// RotateLog 轮换日志文件（简单实现）
func (l *Logger) RotateLog() error {
	if l.config.File == "" {
		return fmt.Errorf("未配置日志文件")
	}

	// 关闭当前文件
	if l.file != nil {
		l.file.Close()
		l.file = nil
	}

	// 重命名当前日志文件
	backupFile := l.config.File + ".old"
	if err := os.Rename(l.config.File, backupFile); err != nil {
		// 如果重命名失败，可能是文件不存在，继续创建新文件
		l.Logger.WithError(err).Warn("重命名日志文件失败")
	}

	// 重新设置输出
	return l.setupOutput()
}

// GetStats 获取日志统计信息
func (l *Logger) GetStats() map[string]interface{} {
	stats := make(map[string]interface{})
	
	stats["level"] = l.GetLogLevel()
	stats["file"] = l.GetLogFile()
	
	// 获取日志文件大小
	if l.config.File != "" {
		if info, err := os.Stat(l.config.File); err == nil {
			stats["file_size"] = info.Size()
			stats["file_mod_time"] = info.ModTime()
		}
	}

	return stats
}
