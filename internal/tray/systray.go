package tray

import (
	"fmt"
	"time"

	"github.com/getlantern/systray"
	"github.com/sirupsen/logrus"
	"license-server-wrapper/internal/vmware"
	"license-server-wrapper/pkg/types"
)

// SysTray 系统托盘管理器
type SysTray struct {
	vmManager *vmware.Manager
	config    *types.Config
	logger    *logrus.Logger
	
	// 菜单项
	mStart    *systray.MenuItem
	mStop     *systray.MenuItem
	mRestart  *systray.MenuItem
	mStatus   *systray.MenuItem
	mSep1     *systray.MenuItem
	mConfig   *systray.MenuItem
	mLogs     *systray.MenuItem
	mSep2     *systray.MenuItem
	mExit     *systray.MenuItem
	
	// 状态
	currentState types.VMState
	
	// 控制
	running bool
	done    chan bool
}

// NewSysTray 创建新的系统托盘
func NewSysTray(vmManager *vmware.Manager, config *types.Config, logger *logrus.Logger) *SysTray {
	return &SysTray{
		vmManager: vmManager,
		config:    config,
		logger:    logger,
		done:      make(chan bool),
	}
}

// Run 运行系统托盘
func (s *SysTray) Run() {
	s.running = true
	systray.Run(s.onReady, s.onExit)
}

// Stop 停止系统托盘
func (s *SysTray) Stop() {
	if s.running {
		s.running = false
		s.done <- true
		systray.Quit()
	}
}

// onReady 托盘就绪回调
func (s *SysTray) onReady() {
	s.logger.Info("系统托盘初始化")
	
	// 设置托盘图标和标题
	s.updateTrayIcon(types.VMStateUnknown)
	systray.SetTitle("VM Manager")
	systray.SetTooltip("VMware虚拟机管理器")

	// 创建菜单
	s.createMenu()
	
	// 设置虚拟机状态变化回调
	s.vmManager.SetStateChangeCallback(s.onVMStateChange)
	
	// 开始监控虚拟机状态
	s.vmManager.StartMonitoring()
	
	// 初始化状态
	s.updateStatus()
	
	// 启动事件处理循环
	go s.eventLoop()
	
	s.logger.Info("系统托盘启动完成")
}

// onExit 托盘退出回调
func (s *SysTray) onExit() {
	s.logger.Info("系统托盘退出")
	s.vmManager.StopMonitoring()
}

// createMenu 创建右键菜单
func (s *SysTray) createMenu() {
	// 虚拟机控制菜单
	s.mStart = systray.AddMenuItem("启动虚拟机", "启动虚拟机")
	s.mStop = systray.AddMenuItem("停止虚拟机", "停止虚拟机")
	s.mRestart = systray.AddMenuItem("重启虚拟机", "重启虚拟机")
	
	s.mSep1 = systray.AddSeparator()
	
	// 状态和信息菜单
	s.mStatus = systray.AddMenuItem("状态: 未知", "查看虚拟机状态")
	s.mStatus.Disable() // 状态菜单项不可点击
	
	s.mSep2 = systray.AddSeparator()
	
	// 配置和日志菜单
	s.mConfig = systray.AddMenuItem("配置", "打开配置")
	s.mLogs = systray.AddMenuItem("查看日志", "查看应用程序日志")
	
	systray.AddSeparator()
	
	// 退出菜单
	s.mExit = systray.AddMenuItem("退出", "退出应用程序")
}

// eventLoop 事件处理循环
func (s *SysTray) eventLoop() {
	for s.running {
		select {
		case <-s.mStart.ClickedCh:
			s.handleStartVM()
		case <-s.mStop.ClickedCh:
			s.handleStopVM()
		case <-s.mRestart.ClickedCh:
			s.handleRestartVM()
		case <-s.mConfig.ClickedCh:
			s.handleConfig()
		case <-s.mLogs.ClickedCh:
			s.handleLogs()
		case <-s.mExit.ClickedCh:
			s.handleExit()
		case <-s.done:
			return
		}
	}
}

// handleStartVM 处理启动虚拟机
func (s *SysTray) handleStartVM() {
	s.logger.Info("用户请求启动虚拟机")
	
	// 禁用启动按钮，防止重复点击
	s.mStart.Disable()
	defer s.mStart.Enable()
	
	// 更新状态显示
	s.mStatus.SetTitle("状态: 启动中...")
	
	// 启动虚拟机
	result, err := s.vmManager.Start()
	if err != nil {
		s.logger.WithError(err).Error("启动虚拟机失败")
		s.showNotification("启动失败", fmt.Sprintf("启动虚拟机失败: %s", err.Error()), false)
		return
	}
	
	if result.Success {
		s.logger.Info("虚拟机启动成功")
		s.showNotification("启动成功", "虚拟机已成功启动", true)
	} else {
		s.logger.WithError(result.Error).Error("启动虚拟机失败")
		s.showNotification("启动失败", result.Message, false)
	}
}

// handleStopVM 处理停止虚拟机
func (s *SysTray) handleStopVM() {
	s.logger.Info("用户请求停止虚拟机")
	
	// 禁用停止按钮，防止重复点击
	s.mStop.Disable()
	defer s.mStop.Enable()
	
	// 更新状态显示
	s.mStatus.SetTitle("状态: 停止中...")
	
	// 停止虚拟机
	result, err := s.vmManager.Stop(false) // 软停止
	if err != nil {
		s.logger.WithError(err).Error("停止虚拟机失败")
		s.showNotification("停止失败", fmt.Sprintf("停止虚拟机失败: %s", err.Error()), false)
		return
	}
	
	if result.Success {
		s.logger.Info("虚拟机停止成功")
		s.showNotification("停止成功", "虚拟机已成功停止", true)
	} else {
		s.logger.WithError(result.Error).Error("停止虚拟机失败")
		s.showNotification("停止失败", result.Message, false)
	}
}

// handleRestartVM 处理重启虚拟机
func (s *SysTray) handleRestartVM() {
	s.logger.Info("用户请求重启虚拟机")
	
	// 禁用重启按钮，防止重复点击
	s.mRestart.Disable()
	defer s.mRestart.Enable()
	
	// 更新状态显示
	s.mStatus.SetTitle("状态: 重启中...")
	
	// 重启虚拟机
	result, err := s.vmManager.Restart(false) // 软重启
	if err != nil {
		s.logger.WithError(err).Error("重启虚拟机失败")
		s.showNotification("重启失败", fmt.Sprintf("重启虚拟机失败: %s", err.Error()), false)
		return
	}
	
	if result.Success {
		s.logger.Info("虚拟机重启成功")
		s.showNotification("重启成功", "虚拟机已成功重启", true)
	} else {
		s.logger.WithError(result.Error).Error("重启虚拟机失败")
		s.showNotification("重启失败", result.Message, false)
	}
}

// handleConfig 处理配置
func (s *SysTray) handleConfig() {
	s.logger.Info("用户请求打开配置")
	// TODO: 实现配置界面
	s.showNotification("配置", "配置功能正在开发中", true)
}

// handleLogs 处理查看日志
func (s *SysTray) handleLogs() {
	s.logger.Info("用户请求查看日志")
	// TODO: 实现日志查看器
	s.showNotification("日志", "日志查看功能正在开发中", true)
}

// handleExit 处理退出
func (s *SysTray) handleExit() {
	s.logger.Info("用户请求退出应用程序")
	s.Stop()
}

// onVMStateChange 虚拟机状态变化回调
func (s *SysTray) onVMStateChange(oldState, newState types.VMState) {
	s.logger.WithFields(logrus.Fields{
		"old_state": oldState.String(),
		"new_state": newState.String(),
	}).Info("虚拟机状态变化")
	
	s.currentState = newState
	s.updateTrayIcon(newState)
	s.updateMenuState(newState)
	s.updateStatus()
	
	// 显示状态变化通知
	if s.config.Tray.ShowNotifications {
		message := fmt.Sprintf("虚拟机状态: %s → %s", oldState.String(), newState.String())
		s.showNotification("状态变化", message, true)
	}
}

// updateTrayIcon 更新托盘图标
func (s *SysTray) updateTrayIcon(state types.VMState) {
	var iconData []byte
	
	switch state {
	case types.VMStateRunning:
		iconData = getRunningIcon()
		systray.SetTooltip("VMware虚拟机管理器 - 运行中")
	case types.VMStateStopped:
		iconData = getStoppedIcon()
		systray.SetTooltip("VMware虚拟机管理器 - 已停止")
	case types.VMStateSuspended:
		iconData = getSuspendedIcon()
		systray.SetTooltip("VMware虚拟机管理器 - 已暂停")
	case types.VMStateError:
		iconData = getErrorIcon()
		systray.SetTooltip("VMware虚拟机管理器 - 错误")
	default:
		iconData = getUnknownIcon()
		systray.SetTooltip("VMware虚拟机管理器 - 未知状态")
	}
	
	if iconData != nil {
		systray.SetIcon(iconData)
	}
}

// updateMenuState 更新菜单状态
func (s *SysTray) updateMenuState(state types.VMState) {
	switch state {
	case types.VMStateRunning:
		s.mStart.Disable()
		s.mStop.Enable()
		s.mRestart.Enable()
	case types.VMStateStopped:
		s.mStart.Enable()
		s.mStop.Disable()
		s.mRestart.Disable()
	case types.VMStateSuspended:
		s.mStart.Enable()
		s.mStop.Enable()
		s.mRestart.Enable()
	default:
		s.mStart.Enable()
		s.mStop.Enable()
		s.mRestart.Enable()
	}
}

// updateStatus 更新状态显示
func (s *SysTray) updateStatus() {
	state := s.vmManager.GetState()
	statusText := fmt.Sprintf("状态: %s", state.String())
	s.mStatus.SetTitle(statusText)
}

// showNotification 显示通知
func (s *SysTray) showNotification(title, message string, success bool) {
	if !s.config.Tray.ShowNotifications {
		return
	}
	
	// 记录通知到日志
	s.logger.WithFields(logrus.Fields{
		"title":   title,
		"message": message,
		"success": success,
	}).Info("显示通知")
	
	// TODO: 实现系统通知
	// 在Windows上可以使用toast通知
	// 这里暂时只记录到日志
}
