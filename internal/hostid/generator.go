package hostid

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"fmt"
	"io"
	"time"

	"github.com/sirupsen/logrus"
)

// Generator 主机ID生成器
type Generator struct {
	logger *logrus.Logger
	key    []byte // AES-256密钥
}

// NewGenerator 创建新的主机ID生成器
func NewGenerator(logger *logrus.Logger) *Generator {
	return &Generator{
		logger: logger,
		key:    deriveKey(), // 从硬件信息派生密钥
	}
}

// GenerateHostID 生成加密的主机ID
func (g *Generator) GenerateHostID() (string, error) {
	// 获取硬件信息
	hwInfo, err := GetHardwareInfo()
	if err != nil {
		g.logger.WithError(err).Error("获取硬件信息失败")
		return "", fmt.Errorf("获取硬件信息失败: %w", err)
	}

	// 生成基础ID字符串（基于硬件信息的固定标识）
	baseID := hwInfo.GetHardwareID()

	// 计算哈希
	hash := sha256.Sum256([]byte(baseID))

	// 加密哈希值
	encryptedID, err := g.encrypt(hash[:])
	if err != nil {
		g.logger.WithError(err).Error("加密主机ID失败")
		return "", fmt.Errorf("加密主机ID失败: %w", err)
	}

	// Base64编码
	encodedID := base64.StdEncoding.EncodeToString(encryptedID)

	g.logger.WithFields(logrus.Fields{
		"hostname": hwInfo.HostName,
		"id_length": len(encodedID),
	}).Info("成功生成主机ID")

	return encodedID, nil
}

// ValidateHostID 验证主机ID是否有效
func (g *Generator) ValidateHostID(encryptedID string) (bool, error) {
	// Base64解码
	encryptedData, err := base64.StdEncoding.DecodeString(encryptedID)
	if err != nil {
		g.logger.WithError(err).Error("Base64解码失败")
		return false, fmt.Errorf("Base64解码失败: %w", err)
	}

	// 解密
	decryptedData, err := g.decrypt(encryptedData)
	if err != nil {
		g.logger.WithError(err).Error("解密主机ID失败")
		return false, fmt.Errorf("解密主机ID失败: %w", err)
	}

	// 获取当前硬件信息
	hwInfo, err := GetHardwareInfo()
	if err != nil {
		g.logger.WithError(err).Error("获取当前硬件信息失败")
		return false, fmt.Errorf("获取当前硬件信息失败: %w", err)
	}

	// 生成当前硬件的哈希（不包含时间戳）
	baseID := hwInfo.GetHardwareID()
	
	// 验证解密后的数据是否包含当前硬件信息的哈希
	// 这里简化验证逻辑，实际应用中可能需要更复杂的验证
	currentHash := sha256.Sum256([]byte(baseID))
	
	// 比较前16字节（简化验证）
	if len(decryptedData) >= 16 && len(currentHash) >= 16 {
		for i := 0; i < 16; i++ {
			if decryptedData[i] != currentHash[i] {
				g.logger.Warn("主机ID验证失败：硬件信息不匹配")
				return false, nil
			}
		}
	}

	g.logger.Info("主机ID验证成功")
	return true, nil
}

// RotateHostID 轮换主机ID（生成新的ID）
func (g *Generator) RotateHostID() (string, error) {
	g.logger.Info("开始轮换主机ID")
	
	// 等待一小段时间确保时间戳不同
	time.Sleep(100 * time.Millisecond)
	
	newID, err := g.GenerateHostID()
	if err != nil {
		g.logger.WithError(err).Error("轮换主机ID失败")
		return "", err
	}

	g.logger.Info("主机ID轮换成功")
	return newID, nil
}

// encrypt 使用AES-GCM加密数据
func (g *Generator) encrypt(data []byte) ([]byte, error) {
	block, err := aes.NewCipher(g.key)
	if err != nil {
		return nil, err
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, err
	}

	nonce := make([]byte, gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return nil, err
	}

	ciphertext := gcm.Seal(nonce, nonce, data, nil)
	return ciphertext, nil
}

// decrypt 使用AES-GCM解密数据
func (g *Generator) decrypt(data []byte) ([]byte, error) {
	block, err := aes.NewCipher(g.key)
	if err != nil {
		return nil, err
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, err
	}

	nonceSize := gcm.NonceSize()
	if len(data) < nonceSize {
		return nil, fmt.Errorf("密文长度不足")
	}

	nonce, ciphertext := data[:nonceSize], data[nonceSize:]
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return nil, err
	}

	return plaintext, nil
}

// deriveKey 从硬件信息派生AES密钥
func deriveKey() []byte {
	// 获取硬件信息作为密钥种子
	hwInfo, err := GetHardwareInfo()
	if err != nil {
		// 如果获取硬件信息失败，使用默认种子
		seed := "default-seed-for-vm-manager-2024"
		hash := sha256.Sum256([]byte(seed))
		return hash[:]
	}

	// 使用硬件信息生成密钥
	keyMaterial := fmt.Sprintf("%s-%s-%s", hwInfo.CPUID, hwInfo.DiskSerial, hwInfo.MACAddress)
	hash := sha256.Sum256([]byte(keyMaterial))
	return hash[:]
}
