package vmware

import (
	"fmt"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
	"license-server-wrapper/internal/hostid"
	"license-server-wrapper/internal/security"
	"license-server-wrapper/pkg/types"
)

// Manager 虚拟机管理器
type Manager struct {
	vmrun      *VMRun
	config     *types.Config
	logger     *logrus.Logger
	hostIDGen  *hostid.Generator
	security   *security.Manager
	
	// 状态管理
	currentState types.VMState
	stateMutex   sync.RWMutex
	
	// 监控
	monitoring   bool
	monitorStop  chan bool
	monitorMutex sync.Mutex
	
	// 事件回调
	onStateChange func(oldState, newState types.VMState)
}

// NewManager 创建新的虚拟机管理器
func NewManager(config *types.Config, logger *logrus.Logger) (*Manager, error) {
	vmrun := NewVMRun(config.VMRunPath, logger)
	hostIDGen := hostid.NewGenerator(logger)
	securityMgr := security.NewManager(logger)

	manager := &Manager{
		vmrun:       vmrun,
		config:      config,
		logger:      logger,
		hostIDGen:   hostIDGen,
		security:    securityMgr,
		currentState: types.VMStateUnknown,
		monitorStop: make(chan bool),
	}

	// 初始化状态
	if err := manager.updateState(); err != nil {
		logger.WithError(err).Warn("初始化虚拟机状态失败")
	}

	return manager, nil
}

// Start 启动虚拟机
func (m *Manager) Start() (*types.VMOperationResult, error) {
	start := time.Now()
	result := &types.VMOperationResult{
		Operation: types.VMOpStart,
		Timestamp: start,
	}

	m.logger.WithField("vm", m.config.VM.Name).Info("开始启动虚拟机")

	// 检查当前状态
	if err := m.updateState(); err != nil {
		result.Error = err
		result.Message = "获取虚拟机状态失败"
		return result, err
	}

	if m.currentState == types.VMStateRunning {
		result.Success = true
		result.Message = "虚拟机已在运行"
		result.NewState = types.VMStateRunning
		result.Duration = time.Since(start)
		return result, nil
	}

	// 启动虚拟机
	if err := m.vmrun.Start(m.config.VM.VMXPath); err != nil {
		result.Error = err
		result.Message = "启动虚拟机失败"
		result.Duration = time.Since(start)
		return result, err
	}

	// 等待虚拟机启动完成
	if err := m.waitForVMReady(); err != nil {
		result.Error = err
		result.Message = "等待虚拟机就绪失败"
		result.Duration = time.Since(start)
		return result, err
	}

	// 如果启用了主机ID验证，执行验证流程
	if m.config.Security.ValidateHostID {
		if err := m.performHostIDValidation(); err != nil {
			m.logger.WithError(err).Error("主机ID验证失败，停止虚拟机")
			// 验证失败，停止虚拟机
			m.vmrun.Stop(m.config.VM.VMXPath, false)
			result.Error = err
			result.Message = "主机ID验证失败"
			result.Duration = time.Since(start)
			return result, err
		}
	}

	// 更新状态
	m.updateState()
	
	result.Success = true
	result.Message = "虚拟机启动成功"
	result.NewState = m.currentState
	result.Duration = time.Since(start)

	m.logger.WithFields(logrus.Fields{
		"vm":       m.config.VM.Name,
		"duration": result.Duration,
	}).Info("虚拟机启动成功")

	return result, nil
}

// Stop 停止虚拟机
func (m *Manager) Stop(force bool) (*types.VMOperationResult, error) {
	start := time.Now()
	result := &types.VMOperationResult{
		Operation: types.VMOpStop,
		Timestamp: start,
	}

	m.logger.WithFields(logrus.Fields{
		"vm":    m.config.VM.Name,
		"force": force,
	}).Info("开始停止虚拟机")

	// 检查当前状态
	if err := m.updateState(); err != nil {
		result.Error = err
		result.Message = "获取虚拟机状态失败"
		return result, err
	}

	if m.currentState == types.VMStateStopped {
		result.Success = true
		result.Message = "虚拟机已停止"
		result.NewState = types.VMStateStopped
		result.Duration = time.Since(start)
		return result, nil
	}

	// 停止虚拟机
	if err := m.vmrun.Stop(m.config.VM.VMXPath, !force); err != nil {
		result.Error = err
		result.Message = "停止虚拟机失败"
		result.Duration = time.Since(start)
		return result, err
	}

	// 更新状态
	m.updateState()
	
	result.Success = true
	result.Message = "虚拟机停止成功"
	result.NewState = m.currentState
	result.Duration = time.Since(start)

	m.logger.WithFields(logrus.Fields{
		"vm":       m.config.VM.Name,
		"duration": result.Duration,
	}).Info("虚拟机停止成功")

	return result, nil
}

// Restart 重启虚拟机
func (m *Manager) Restart(force bool) (*types.VMOperationResult, error) {
	start := time.Now()
	result := &types.VMOperationResult{
		Operation: types.VMOpRestart,
		Timestamp: start,
	}

	m.logger.WithField("vm", m.config.VM.Name).Info("开始重启虚拟机")

	// 先停止
	stopResult, err := m.Stop(force)
	if err != nil && stopResult.NewState != types.VMStateStopped {
		result.Error = err
		result.Message = "停止虚拟机失败"
		result.Duration = time.Since(start)
		return result, err
	}

	// 等待一段时间
	time.Sleep(2 * time.Second)

	// 再启动
	startResult, err := m.Start()
	if err != nil {
		result.Error = err
		result.Message = "启动虚拟机失败"
		result.Duration = time.Since(start)
		return result, err
	}

	result.Success = true
	result.Message = "虚拟机重启成功"
	result.NewState = startResult.NewState
	result.Duration = time.Since(start)

	m.logger.WithFields(logrus.Fields{
		"vm":       m.config.VM.Name,
		"duration": result.Duration,
	}).Info("虚拟机重启成功")

	return result, nil
}

// GetState 获取当前虚拟机状态
func (m *Manager) GetState() types.VMState {
	m.stateMutex.RLock()
	defer m.stateMutex.RUnlock()
	return m.currentState
}

// updateState 更新虚拟机状态
func (m *Manager) updateState() error {
	newState, err := m.vmrun.GetState(m.config.VM.VMXPath)
	if err != nil {
		return err
	}

	m.stateMutex.Lock()
	oldState := m.currentState
	m.currentState = newState
	m.stateMutex.Unlock()

	// 如果状态发生变化，触发回调
	if oldState != newState && m.onStateChange != nil {
		m.onStateChange(oldState, newState)
	}

	return nil
}

// SetStateChangeCallback 设置状态变化回调
func (m *Manager) SetStateChangeCallback(callback func(oldState, newState types.VMState)) {
	m.onStateChange = callback
}

// StartMonitoring 开始监控虚拟机状态
func (m *Manager) StartMonitoring() {
	m.monitorMutex.Lock()
	defer m.monitorMutex.Unlock()

	if m.monitoring {
		return
	}

	m.monitoring = true
	go m.monitorLoop()
	m.logger.Info("开始监控虚拟机状态")
}

// StopMonitoring 停止监控虚拟机状态
func (m *Manager) StopMonitoring() {
	m.monitorMutex.Lock()
	defer m.monitorMutex.Unlock()

	if !m.monitoring {
		return
	}

	m.monitoring = false
	m.monitorStop <- true
	m.logger.Info("停止监控虚拟机状态")
}

// monitorLoop 监控循环
func (m *Manager) monitorLoop() {
	ticker := time.NewTicker(time.Duration(m.config.Tray.UpdateInterval) * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if err := m.updateState(); err != nil {
				m.logger.WithError(err).Error("更新虚拟机状态失败")
			}
		case <-m.monitorStop:
			return
		}
	}
}

// waitForVMReady 等待虚拟机就绪
func (m *Manager) waitForVMReady() error {
	timeout := time.Duration(m.config.Security.ValidationTimeout) * time.Second
	return m.vmrun.WaitForToolsInGuest(m.config.VM.VMXPath, timeout)
}

// performHostIDValidation 执行主机ID验证
func (m *Manager) performHostIDValidation() error {
	m.logger.Info("开始主机ID验证流程")

	// 生成当前主机ID
	hostID, err := m.hostIDGen.GenerateHostID()
	if err != nil {
		return fmt.Errorf("生成主机ID失败: %w", err)
	}

	// 获取解密后的密码
	password, err := m.security.DecryptPassword(m.config.VM.Password)
	if err != nil {
		return fmt.Errorf("解密虚拟机密码失败: %w", err)
	}

	// 将主机ID传递给客户机
	if err := m.vmrun.SetGuestVar(m.config.VM.VMXPath, m.config.VM.Username, 
		password, "host_id", hostID); err != nil {
		return fmt.Errorf("设置客户机主机ID失败: %w", err)
	}

	// 触发客户机验证脚本
	if err := m.vmrun.RunProgramInGuest(m.config.VM.VMXPath, m.config.VM.Username, 
		password, "C:\\validator.bat"); err != nil {
		return fmt.Errorf("运行客户机验证脚本失败: %w", err)
	}

	// 等待验证结果
	if err := m.waitForValidationResult(password); err != nil {
		return err
	}

	// 如果配置了轮换ID，生成新的ID
	if m.config.Security.RotateIDOnSuccess {
		if _, err := m.hostIDGen.RotateHostID(); err != nil {
			m.logger.WithError(err).Warn("轮换主机ID失败")
		}
	}

	m.logger.Info("主机ID验证成功")
	return nil
}

// waitForValidationResult 等待验证结果
func (m *Manager) waitForValidationResult(password string) error {
	timeout := time.Duration(m.config.Security.ValidationTimeout) * time.Second
	start := time.Now()

	for time.Since(start) < timeout {
		// 检查验证结果
		result, err := m.vmrun.GetGuestVar(m.config.VM.VMXPath, m.config.VM.Username, 
			password, "validation_result")
		if err == nil && result == "success" {
			return nil
		}

		// 检查是否验证失败
		if err == nil && result == "failed" {
			return fmt.Errorf("客户机验证失败")
		}

		time.Sleep(1 * time.Second)
	}

	return fmt.Errorf("等待验证结果超时")
}
