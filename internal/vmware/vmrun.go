package vmware

import (
	"bufio"
	"fmt"
	"os/exec"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
	"license-server-wrapper/pkg/types"
)

// VMRun VMware vmrun命令封装器
type VMRun struct {
	vmrunPath string
	logger    *logrus.Logger
}

// NewVMRun 创建新的VMRun实例
func NewVMRun(vmrunPath string, logger *logrus.Logger) *VMRun {
	return &VMRun{
		vmrunPath: vmrunPath,
		logger:    logger,
	}
}

// Start 启动虚拟机
func (v *VMRun) Start(vmxPath string) error {
	v.logger.WithField("vmx", vmxPath).Info("启动虚拟机")
	
	cmd := exec.Command(v.vmrunPath, "-T", "ws", "start", vmxPath)
	output, err := cmd.CombinedOutput()
	
	if err != nil {
		v.logger.WithError(err).WithField("output", string(output)).Error("启动虚拟机失败")
		return fmt.Errorf("启动虚拟机失败: %w, 输出: %s", err, string(output))
	}

	v.logger.WithField("vmx", vmxPath).Info("虚拟机启动成功")
	return nil
}

// Stop 停止虚拟机
func (v *VMRun) Stop(vmxPath string, soft bool) error {
	v.logger.WithFields(logrus.Fields{
		"vmx":  vmxPath,
		"soft": soft,
	}).Info("停止虚拟机")

	var cmd *exec.Cmd
	if soft {
		cmd = exec.Command(v.vmrunPath, "-T", "ws", "stop", vmxPath, "soft")
	} else {
		cmd = exec.Command(v.vmrunPath, "-T", "ws", "stop", vmxPath, "hard")
	}

	output, err := cmd.CombinedOutput()
	
	if err != nil {
		v.logger.WithError(err).WithField("output", string(output)).Error("停止虚拟机失败")
		return fmt.Errorf("停止虚拟机失败: %w, 输出: %s", err, string(output))
	}

	v.logger.WithField("vmx", vmxPath).Info("虚拟机停止成功")
	return nil
}

// Reset 重置虚拟机
func (v *VMRun) Reset(vmxPath string, soft bool) error {
	v.logger.WithFields(logrus.Fields{
		"vmx":  vmxPath,
		"soft": soft,
	}).Info("重置虚拟机")

	var cmd *exec.Cmd
	if soft {
		cmd = exec.Command(v.vmrunPath, "-T", "ws", "reset", vmxPath, "soft")
	} else {
		cmd = exec.Command(v.vmrunPath, "-T", "ws", "reset", vmxPath, "hard")
	}

	output, err := cmd.CombinedOutput()
	
	if err != nil {
		v.logger.WithError(err).WithField("output", string(output)).Error("重置虚拟机失败")
		return fmt.Errorf("重置虚拟机失败: %w, 输出: %s", err, string(output))
	}

	v.logger.WithField("vmx", vmxPath).Info("虚拟机重置成功")
	return nil
}

// Suspend 暂停虚拟机
func (v *VMRun) Suspend(vmxPath string) error {
	v.logger.WithField("vmx", vmxPath).Info("暂停虚拟机")
	
	cmd := exec.Command(v.vmrunPath, "-T", "ws", "suspend", vmxPath)
	output, err := cmd.CombinedOutput()
	
	if err != nil {
		v.logger.WithError(err).WithField("output", string(output)).Error("暂停虚拟机失败")
		return fmt.Errorf("暂停虚拟机失败: %w, 输出: %s", err, string(output))
	}

	v.logger.WithField("vmx", vmxPath).Info("虚拟机暂停成功")
	return nil
}

// GetState 获取虚拟机状态
func (v *VMRun) GetState(vmxPath string) (types.VMState, error) {
	cmd := exec.Command(v.vmrunPath, "list")
	output, err := cmd.CombinedOutput()
	
	if err != nil {
		v.logger.WithError(err).WithField("output", string(output)).Error("获取虚拟机列表失败")
		return types.VMStateUnknown, fmt.Errorf("获取虚拟机列表失败: %w", err)
	}

	outputStr := string(output)
	
	// 检查虚拟机是否在运行列表中
	if strings.Contains(outputStr, vmxPath) {
		return types.VMStateRunning, nil
	}

	// 检查是否暂停状态
	cmd = exec.Command(v.vmrunPath, "-T", "ws", "checkToolsState", vmxPath)
	output, err = cmd.CombinedOutput()
	
	if err != nil {
		// 如果checkToolsState失败，可能虚拟机已停止
		return types.VMStateStopped, nil
	}

	outputStr = string(output)
	if strings.Contains(strings.ToLower(outputStr), "suspended") {
		return types.VMStateSuspended, nil
	}

	return types.VMStateStopped, nil
}

// SetGuestVar 设置客户机变量（不需要用户名密码）
func (v *VMRun) SetGuestVar(vmxPath, varName, varValue string) error {
	v.logger.WithFields(logrus.Fields{
		"vmx":     vmxPath,
		"varName": varName,
	}).Info("设置客户机变量")

	cmd := exec.Command(v.vmrunPath, "-T", "ws", "writeVariable", vmxPath, "guestVar", varName, varValue)

	output, err := cmd.CombinedOutput()

	if err != nil {
		v.logger.WithError(err).WithField("output", string(output)).Error("设置客户机变量失败")
		return fmt.Errorf("设置客户机变量失败: %w, 输出: %s", err, string(output))
	}

	v.logger.WithField("varName", varName).Info("客户机变量设置成功")
	return nil
}

// GetGuestVar 获取客户机变量（不需要用户名密码）
func (v *VMRun) GetGuestVar(vmxPath, varName string) (string, error) {
	v.logger.WithFields(logrus.Fields{
		"vmx":     vmxPath,
		"varName": varName,
	}).Info("获取客户机变量")

	cmd := exec.Command(v.vmrunPath, "-T", "ws", "readVariable", vmxPath, "guestVar", varName)

	output, err := cmd.CombinedOutput()

	if err != nil {
		v.logger.WithError(err).WithField("output", string(output)).Error("获取客户机变量失败")
		return "", fmt.Errorf("获取客户机变量失败: %w, 输出: %s", err, string(output))
	}

	value := strings.TrimSpace(string(output))
	v.logger.WithFields(logrus.Fields{
		"varName": varName,
		"value":   value,
	}).Info("客户机变量获取成功")

	return value, nil
}

// RunProgramInGuest 在客户机中运行程序
func (v *VMRun) RunProgramInGuest(vmxPath, username, password, program string, args ...string) error {
	v.logger.WithFields(logrus.Fields{
		"vmx":     vmxPath,
		"user":    username,
		"program": program,
		"args":    args,
	}).Info("在客户机中运行程序")

	cmdArgs := []string{"-T", "ws", "-gu", username, "-gp", password, 
		"runProgramInGuest", vmxPath, program}
	cmdArgs = append(cmdArgs, args...)
	
	cmd := exec.Command(v.vmrunPath, cmdArgs...)
	output, err := cmd.CombinedOutput()
	
	if err != nil {
		v.logger.WithError(err).WithField("output", string(output)).Error("在客户机中运行程序失败")
		return fmt.Errorf("在客户机中运行程序失败: %w, 输出: %s", err, string(output))
	}

	v.logger.WithField("program", program).Info("客户机程序运行成功")
	return nil
}

// WaitForToolsInGuest 等待VMware Tools启动
func (v *VMRun) WaitForToolsInGuest(vmxPath string, timeout time.Duration) error {
	v.logger.WithFields(logrus.Fields{
		"vmx":     vmxPath,
		"timeout": timeout,
	}).Info("等待VMware Tools启动")

	start := time.Now()
	for time.Since(start) < timeout {
		cmd := exec.Command(v.vmrunPath, "-T", "ws", "checkToolsState", vmxPath)
		output, err := cmd.CombinedOutput()
		
		if err == nil {
			outputStr := strings.ToLower(string(output))
			if strings.Contains(outputStr, "running") {
				v.logger.Info("VMware Tools已启动")
				return nil
			}
		}

		time.Sleep(2 * time.Second)
	}

	return fmt.Errorf("等待VMware Tools启动超时")
}

// GetRunningVMs 获取正在运行的虚拟机列表
func (v *VMRun) GetRunningVMs() ([]string, error) {
	cmd := exec.Command(v.vmrunPath, "list")
	output, err := cmd.CombinedOutput()
	
	if err != nil {
		v.logger.WithError(err).Error("获取运行中的虚拟机列表失败")
		return nil, fmt.Errorf("获取运行中的虚拟机列表失败: %w", err)
	}

	var vms []string
	scanner := bufio.NewScanner(strings.NewReader(string(output)))
	
	// 跳过第一行（总数）
	if scanner.Scan() {
		firstLine := scanner.Text()
		// 解析总数
		re := regexp.MustCompile(`Total running VMs: (\d+)`)
		matches := re.FindStringSubmatch(firstLine)
		if len(matches) > 1 {
			count, _ := strconv.Atoi(matches[1])
			if count == 0 {
				return vms, nil
			}
		}
	}

	// 读取虚拟机路径
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line != "" {
			vms = append(vms, line)
		}
	}

	return vms, nil
}

// IsVMRunning 检查指定虚拟机是否正在运行
func (v *VMRun) IsVMRunning(vmxPath string) (bool, error) {
	runningVMs, err := v.GetRunningVMs()
	if err != nil {
		return false, err
	}

	for _, vm := range runningVMs {
		if vm == vmxPath {
			return true, nil
		}
	}

	return false, nil
}
