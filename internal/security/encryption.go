package security

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"fmt"
	"io"
	"runtime"

	"github.com/sirupsen/logrus"
	"golang.org/x/sys/windows/registry"
)

// Manager 安全管理器
type Manager struct {
	logger *logrus.Logger
	key    []byte
}

// NewManager 创建新的安全管理器
func NewManager(logger *logrus.Logger) *Manager {
	return &Manager{
		logger: logger,
		key:    deriveEncryptionKey(),
	}
}

// EncryptPassword 加密密码
func (m *Manager) EncryptPassword(password string) (string, error) {
	if password == "" {
		return "", nil
	}

	m.logger.Debug("加密密码")

	// 在Windows上使用DPAPI
	if runtime.GOOS == "windows" {
		return m.encryptWithDPAPI(password)
	}

	// 其他系统使用AES加密
	return m.encryptWithAES(password)
}

// DecryptPassword 解密密码
func (m *Manager) DecryptPassword(encryptedPassword string) (string, error) {
	if encryptedPassword == "" {
		return "", nil
	}

	m.logger.Debug("解密密码")

	// 在Windows上使用DPAPI
	if runtime.GOOS == "windows" {
		return m.decryptWithDPAPI(encryptedPassword)
	}

	// 其他系统使用AES解密
	return m.decryptWithAES(encryptedPassword)
}

// encryptWithDPAPI 使用Windows DPAPI加密
func (m *Manager) encryptWithDPAPI(data string) (string, error) {
	// 注意：这里简化实现，实际应用中应该使用Windows API
	// 由于Go标准库不直接支持DPAPI，这里使用AES作为替代
	m.logger.Warn("DPAPI未实现，使用AES加密替代")
	return m.encryptWithAES(data)
}

// decryptWithDPAPI 使用Windows DPAPI解密
func (m *Manager) decryptWithDPAPI(encryptedData string) (string, error) {
	// 注意：这里简化实现，实际应用中应该使用Windows API
	m.logger.Warn("DPAPI未实现，使用AES解密替代")
	return m.decryptWithAES(encryptedData)
}

// encryptWithAES 使用AES加密
func (m *Manager) encryptWithAES(data string) (string, error) {
	block, err := aes.NewCipher(m.key)
	if err != nil {
		return "", fmt.Errorf("创建AES密码器失败: %w", err)
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", fmt.Errorf("创建GCM失败: %w", err)
	}

	nonce := make([]byte, gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return "", fmt.Errorf("生成随机数失败: %w", err)
	}

	ciphertext := gcm.Seal(nonce, nonce, []byte(data), nil)
	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// decryptWithAES 使用AES解密
func (m *Manager) decryptWithAES(encryptedData string) (string, error) {
	data, err := base64.StdEncoding.DecodeString(encryptedData)
	if err != nil {
		return "", fmt.Errorf("Base64解码失败: %w", err)
	}

	block, err := aes.NewCipher(m.key)
	if err != nil {
		return "", fmt.Errorf("创建AES密码器失败: %w", err)
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", fmt.Errorf("创建GCM失败: %w", err)
	}

	nonceSize := gcm.NonceSize()
	if len(data) < nonceSize {
		return "", fmt.Errorf("密文长度不足")
	}

	nonce, ciphertext := data[:nonceSize], data[nonceSize:]
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return "", fmt.Errorf("解密失败: %w", err)
	}

	return string(plaintext), nil
}

// deriveEncryptionKey 派生加密密钥
func deriveEncryptionKey() []byte {
	var keyMaterial string

	// 尝试从系统获取唯一标识
	if runtime.GOOS == "windows" {
		// 尝试从注册表获取机器GUID
		if guid := getMachineGUID(); guid != "" {
			keyMaterial = guid
		}
	}

	// 如果无法获取系统标识，使用默认种子
	if keyMaterial == "" {
		keyMaterial = "default-vm-manager-key-2024"
	}

	// 使用SHA256生成32字节密钥
	hash := sha256.Sum256([]byte(keyMaterial))
	return hash[:]
}

// getMachineGUID 获取Windows机器GUID
func getMachineGUID() string {
	if runtime.GOOS != "windows" {
		return ""
	}

	// 尝试从注册表读取机器GUID
	key, err := registry.OpenKey(registry.LOCAL_MACHINE, 
		`SOFTWARE\Microsoft\Cryptography`, registry.QUERY_VALUE)
	if err != nil {
		return ""
	}
	defer key.Close()

	guid, _, err := key.GetStringValue("MachineGuid")
	if err != nil {
		return ""
	}

	return guid
}

// SecureString 安全字符串，用于在内存中安全存储敏感数据
type SecureString struct {
	data []byte
}

// NewSecureString 创建安全字符串
func NewSecureString(data string) *SecureString {
	return &SecureString{
		data: []byte(data),
	}
}

// String 获取字符串值
func (s *SecureString) String() string {
	return string(s.data)
}

// Clear 清除内存中的数据
func (s *SecureString) Clear() {
	for i := range s.data {
		s.data[i] = 0
	}
	s.data = nil
}

// Bytes 获取字节数组
func (s *SecureString) Bytes() []byte {
	return s.data
}

// IsEmpty 检查是否为空
func (s *SecureString) IsEmpty() bool {
	return len(s.data) == 0
}

// ValidateIntegrity 验证数据完整性
func (m *Manager) ValidateIntegrity(data, signature string) bool {
	// 简单的完整性验证实现
	expectedSignature := m.generateSignature(data)
	return expectedSignature == signature
}

// GenerateSignature 生成数据签名
func (m *Manager) GenerateSignature(data string) string {
	return m.generateSignature(data)
}

// generateSignature 内部签名生成方法
func (m *Manager) generateSignature(data string) string {
	hash := sha256.Sum256([]byte(data + string(m.key)))
	return base64.StdEncoding.EncodeToString(hash[:])
}

// SecureRandom 生成安全随机数
func (m *Manager) SecureRandom(length int) ([]byte, error) {
	bytes := make([]byte, length)
	if _, err := rand.Read(bytes); err != nil {
		return nil, fmt.Errorf("生成随机数失败: %w", err)
	}
	return bytes, nil
}

// HashPassword 哈希密码（用于存储验证）
func (m *Manager) HashPassword(password string) string {
	hash := sha256.Sum256([]byte(password + string(m.key)))
	return base64.StdEncoding.EncodeToString(hash[:])
}

// VerifyPassword 验证密码哈希
func (m *Manager) VerifyPassword(password, hash string) bool {
	expectedHash := m.HashPassword(password)
	return expectedHash == hash
}

// EncryptData 加密任意数据
func (m *Manager) EncryptData(data []byte) ([]byte, error) {
	block, err := aes.NewCipher(m.key)
	if err != nil {
		return nil, fmt.Errorf("创建AES密码器失败: %w", err)
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("创建GCM失败: %w", err)
	}

	nonce := make([]byte, gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return nil, fmt.Errorf("生成随机数失败: %w", err)
	}

	ciphertext := gcm.Seal(nonce, nonce, data, nil)
	return ciphertext, nil
}

// DecryptData 解密任意数据
func (m *Manager) DecryptData(encryptedData []byte) ([]byte, error) {
	block, err := aes.NewCipher(m.key)
	if err != nil {
		return nil, fmt.Errorf("创建AES密码器失败: %w", err)
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("创建GCM失败: %w", err)
	}

	nonceSize := gcm.NonceSize()
	if len(encryptedData) < nonceSize {
		return nil, fmt.Errorf("密文长度不足")
	}

	nonce, ciphertext := encryptedData[:nonceSize], encryptedData[nonceSize:]
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return nil, fmt.Errorf("解密失败: %w", err)
	}

	return plaintext, nil
}
