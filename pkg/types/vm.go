package types

import "time"

// VMState 虚拟机状态枚举
type VMState int

const (
	// VMStateStopped 虚拟机已停止
	VMStateStopped VMState = iota
	// VMStateRunning 虚拟机正在运行
	VMStateRunning
	// VMStateSuspended 虚拟机已暂停
	VMStateSuspended
	// VMStatePaused 虚拟机已暂停（不同于suspended）
	VMStatePaused
	// VMStateUnknown 虚拟机状态未知
	VMStateUnknown
	// VMStateError 虚拟机出错
	VMStateError
)

// String 返回VM状态的字符串表示
func (s VMState) String() string {
	switch s {
	case VMStateStopped:
		return "已停止"
	case VMStateRunning:
		return "运行中"
	case VMStateSuspended:
		return "已暂停"
	case VMStatePaused:
		return "已暂停"
	case VMStateUnknown:
		return "未知"
	case VMStateError:
		return "错误"
	default:
		return "未定义"
	}
}

// VMInfo 虚拟机信息
type VMInfo struct {
	// VMXPath VMX文件路径
	VMXPath string `yaml:"vmx_path"`
	// Name 虚拟机名称
	Name string `yaml:"name"`
	// Username 虚拟机用户名
	Username string `yaml:"username"`
	// Password 虚拟机密码（加密存储）
	Password string `yaml:"password"`
	// State 当前状态
	State VMState `yaml:"-"`
	// LastStateCheck 最后状态检查时间
	LastStateCheck time.Time `yaml:"-"`
	// HardcodedHostID 硬编码的主机ID（用于客户机验证）
	HardcodedHostID string `yaml:"hardcoded_host_id"`
	// AutoStart 是否自动启动
	AutoStart bool `yaml:"auto_start"`
	// ValidateOnStart 启动时是否验证主机ID
	ValidateOnStart bool `yaml:"validate_on_start"`
}

// VMOperation 虚拟机操作类型
type VMOperation int

const (
	// VMOpStart 启动虚拟机
	VMOpStart VMOperation = iota
	// VMOpStop 停止虚拟机
	VMOpStop
	// VMOpRestart 重启虚拟机
	VMOpRestart
	// VMOpSuspend 暂停虚拟机
	VMOpSuspend
	// VMOpResume 恢复虚拟机
	VMOpResume
	// VMOpReset 重置虚拟机
	VMOpReset
	// VMOpCheckStatus 检查状态
	VMOpCheckStatus
)

// String 返回操作的字符串表示
func (op VMOperation) String() string {
	switch op {
	case VMOpStart:
		return "启动"
	case VMOpStop:
		return "停止"
	case VMOpRestart:
		return "重启"
	case VMOpSuspend:
		return "暂停"
	case VMOpResume:
		return "恢复"
	case VMOpReset:
		return "重置"
	case VMOpCheckStatus:
		return "检查状态"
	default:
		return "未知操作"
	}
}

// VMOperationResult 虚拟机操作结果
type VMOperationResult struct {
	// Operation 执行的操作
	Operation VMOperation
	// Success 是否成功
	Success bool
	// Error 错误信息
	Error error
	// Message 结果消息
	Message string
	// NewState 操作后的新状态
	NewState VMState
	// Duration 操作耗时
	Duration time.Duration
	// Timestamp 操作时间戳
	Timestamp time.Time
}

// SecurityConfig 安全配置
type SecurityConfig struct {
	// EncryptPasswords 是否加密密码
	EncryptPasswords bool `yaml:"encrypt_passwords"`
	// ValidateHostID 是否验证主机ID
	ValidateHostID bool `yaml:"validate_host_id"`
	// RotateIDOnSuccess 验证成功后是否轮换ID
	RotateIDOnSuccess bool `yaml:"rotate_id_on_success"`
	// ValidationTimeout 验证超时时间（秒）
	ValidationTimeout int `yaml:"validation_timeout"`
	// MaxRetries 最大重试次数
	MaxRetries int `yaml:"max_retries"`
}

// TrayConfig 系统托盘配置
type TrayConfig struct {
	// ShowNotifications 是否显示通知
	ShowNotifications bool `yaml:"show_notifications"`
	// AutoHide 是否自动隐藏
	AutoHide bool `yaml:"auto_hide"`
	// UpdateInterval 状态更新间隔（秒）
	UpdateInterval int `yaml:"update_interval"`
	// IconTheme 图标主题
	IconTheme string `yaml:"icon_theme"`
}

// LogConfig 日志配置
type LogConfig struct {
	// Level 日志级别
	Level string `yaml:"level"`
	// File 日志文件路径
	File string `yaml:"file"`
	// MaxSize 最大文件大小（MB）
	MaxSize int `yaml:"max_size"`
	// MaxBackups 最大备份文件数
	MaxBackups int `yaml:"max_backups"`
	// MaxAge 最大保存天数
	MaxAge int `yaml:"max_age"`
	// Compress 是否压缩
	Compress bool `yaml:"compress"`
}

// Config 应用程序配置
type Config struct {
	// VM 虚拟机配置
	VM VMInfo `yaml:"vm"`
	// Security 安全配置
	Security SecurityConfig `yaml:"security"`
	// Tray 系统托盘配置
	Tray TrayConfig `yaml:"tray"`
	// Log 日志配置
	Log LogConfig `yaml:"log"`
	// VMRunPath vmrun可执行文件路径
	VMRunPath string `yaml:"vmrun_path"`
}

// DefaultConfig 返回默认配置
func DefaultConfig() *Config {
	return &Config{
		VM: VMInfo{
			VMXPath:         "",
			Name:            "默认虚拟机",
			Username:        "",
			Password:        "",
			HardcodedHostID: "",
			AutoStart:       false,
			ValidateOnStart: true,
		},
		Security: SecurityConfig{
			EncryptPasswords:  true,
			ValidateHostID:    true,
			RotateIDOnSuccess: true,
			ValidationTimeout: 30,
			MaxRetries:        3,
		},
		Tray: TrayConfig{
			ShowNotifications: true,
			AutoHide:          false,
			UpdateInterval:    5,
			IconTheme:         "default",
		},
		Log: LogConfig{
			Level:      "info",
			File:       "logs/vmmanager.log",
			MaxSize:    10,
			MaxBackups: 5,
			MaxAge:     30,
			Compress:   true,
		},
		VMRunPath: "vmrun.exe",
	}
}
